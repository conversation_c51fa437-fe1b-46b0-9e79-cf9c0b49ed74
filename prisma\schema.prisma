// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  PORTAL_ADMIN
  COMPANY_ADMIN
  USER
}

enum NotificationType {
  INFO
  WARNING
  ERROR
  SUCCESS
}

enum CompanyStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum EinvoiceStatus {
  DRAFT
  PENDING
  APPROVED
  REJECTED
  SENT
  RECEIVED
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String?
  name      String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  companyAccess     UserCompanyAccess[]
  notifications     Notification[]
  activityLogs      UserActivityLog[]
  preferences       UserPreference?
  dashboardConfigs  DashboardConfig[]

  @@map("users")
}

model Country {
  id        String   @id @default(cuid())
  name      String   @unique
  code      String   @unique // ISO country code
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  companies Company[]

  @@map("countries")
}

model Company {
  id              String        @id @default(cuid())
  name            String
  status          CompanyStatus @default(ACTIVE)
  subscriptionEnd DateTime?
  environment     String        @default("production") // "production" or "sandbox"
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Foreign Keys
  countryId String
  country   Country @relation(fields: [countryId], references: [id])

  // Relations
  userAccess       UserCompanyAccess[]
  einvoices        Einvoice[]
  dashboardConfigs DashboardConfig[]

  @@map("companies")
}

model UserCompanyAccess {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign Keys
  userId    String
  companyId String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@unique([userId, companyId])
  @@map("user_company_access")
}

model Einvoice {
  id          String          @id @default(cuid())
  invoiceNo   String
  amount      Decimal
  currency    String          @default("USD")
  status      EinvoiceStatus  @default(DRAFT)
  issueDate   DateTime
  dueDate     DateTime?
  description String?
  metadata    Json? // Store additional invoice data
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Foreign Keys
  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  @@unique([companyId, invoiceNo])
  @@map("einvoices")
}

model Notification {
  id        String           @id @default(cuid())
  title     String
  message   String
  type      NotificationType @default(INFO)
  read      Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Foreign Keys
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model UserActivityLog {
  id          String   @id @default(cuid())
  action      String
  description String?
  metadata    Json? // Store additional context data
  createdAt   DateTime @default(now())

  // Foreign Keys
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_activity_logs")
}

model UserPreference {
  id           String @id @default(cuid())
  theme        String @default("light") // "light" or "dark"
  language     String @default("en")
  timezone     String @default("UTC")
  notifications Json? // Notification preferences

  // Foreign Keys
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model DashboardConfig {
  id       String @id @default(cuid())
  name     String
  layout   Json // Store dashboard layout configuration
  widgets  Json // Store widget configurations
  isActive Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign Keys
  userId    String
  companyId String?
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  company   Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@map("dashboard_configs")
}
