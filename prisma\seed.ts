import { PrismaClient, UserRole, CompanyStatus, NotificationType, NotificationPriority, ActionType, ActorType } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create countries
  const countries = await Promise.all([
    prisma.country.upsert({
      where: { code: 'US' },
      update: {},
      create: {
        name: 'United States',
        code: 'US',
      },
    }),
    prisma.country.upsert({
      where: { code: 'CA' },
      update: {},
      create: {
        name: 'Canada',
        code: 'CA',
      },
    }),
    prisma.country.upsert({
      where: { code: 'UK' },
      update: {},
      create: {
        name: 'United Kingdom',
        code: 'UK',
      },
    }),
    prisma.country.upsert({
      where: { code: 'DE' },
      update: {},
      create: {
        name: 'Germany',
        code: 'DE',
      },
    }),
  ])

  console.log('✅ Countries created')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Portal Administrator',
      role: UserRole.PORTAL_ADMIN,
    },
  })

  console.log('✅ Admin user created')

  // Create regular user
  const userPassword = await bcrypt.hash('user123', 12)
  const regularUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: userPassword,
      name: 'John Doe',
      role: UserRole.USER,
    },
  })

  console.log('✅ Regular user created')

  // Create companies
  const companies = await Promise.all([
    prisma.company.upsert({
      where: { id: 'company-1' },
      update: {},
      create: {
        id: 'company-1',
        name: 'Acme Corporation',
        status: CompanyStatus.ACTIVE,
        environment: 'production',
        subscriptionEnd: new Date('2024-12-31'),
        countryId: countries[0].id, // US
      },
    }),
    prisma.company.upsert({
      where: { id: 'company-2' },
      update: {},
      create: {
        id: 'company-2',
        name: 'Acme Corporation - Sandbox',
        status: CompanyStatus.ACTIVE,
        environment: 'sandbox',
        subscriptionEnd: new Date('2024-12-31'),
        countryId: countries[0].id, // US
      },
    }),
    prisma.company.upsert({
      where: { id: 'company-3' },
      update: {},
      create: {
        id: 'company-3',
        name: 'Global Tech Solutions',
        status: CompanyStatus.ACTIVE,
        environment: 'production',
        subscriptionEnd: new Date('2025-06-30'),
        countryId: countries[1].id, // Canada
      },
    }),
  ])

  console.log('✅ Companies created')

  // Create user company access
  await Promise.all([
    prisma.userCompanyAccess.upsert({
      where: {
        userId_companyId: {
          userId: regularUser.id,
          companyId: companies[0].id,
        },
      },
      update: {},
      create: {
        userId: regularUser.id,
        companyId: companies[0].id,
      },
    }),
    prisma.userCompanyAccess.upsert({
      where: {
        userId_companyId: {
          userId: regularUser.id,
          companyId: companies[1].id,
        },
      },
      update: {},
      create: {
        userId: regularUser.id,
        companyId: companies[1].id,
      },
    }),
  ])

  console.log('✅ User company access created')

  // Create sample invoices
  await Promise.all([
    prisma.einvoice.upsert({
      where: {
        companyId_invoiceNo: {
          companyId: companies[0].id,
          invoiceNo: 'INV-001',
        },
      },
      update: {},
      create: {
        invoiceNo: 'INV-001',
        amount: 1500.00,
        currency: 'USD',
        status: 'APPROVED',
        issueDate: new Date('2024-01-15'),
        dueDate: new Date('2024-02-15'),
        description: 'Software development services',
        companyId: companies[0].id,
      },
    }),
    prisma.einvoice.upsert({
      where: {
        companyId_invoiceNo: {
          companyId: companies[0].id,
          invoiceNo: 'INV-002',
        },
      },
      update: {},
      create: {
        invoiceNo: 'INV-002',
        amount: 2500.00,
        currency: 'USD',
        status: 'PENDING',
        issueDate: new Date('2024-01-20'),
        dueDate: new Date('2024-02-20'),
        description: 'Consulting services',
        companyId: companies[0].id,
      },
    }),
  ])

  console.log('✅ Sample invoices created')

  // Create user preferences
  await Promise.all([
    prisma.userPreference.upsert({
      where: { userId: adminUser.id },
      update: {},
      create: {
        userId: adminUser.id,
        receiveEmailNotifications: true,
        receiveInAppNotifications: true,
        receivePushNotifications: false,
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
      },
    }),
    prisma.userPreference.upsert({
      where: { userId: regularUser.id },
      update: {},
      create: {
        userId: regularUser.id,
        receiveEmailNotifications: true,
        receiveInAppNotifications: true,
        receivePushNotifications: true,
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
      },
    }),
  ])

  console.log('✅ User preferences created')

  // Create sample activity logs
  const activityLogs = await Promise.all([
    prisma.userActivityLog.create({
      data: {
        userId: regularUser.id,
        actorType: ActorType.USER,
        actorId: regularUser.id,
        companyId: companies[0].id,
        actionType: ActionType.EINVOICE_CREATED,
        targetType: 'Einvoice',
        targetId: 'einvoice-1',
        details: {
          invoiceNumber: 'INV-001',
          amount: 1500.00,
          currency: 'USD',
        },
        status: 'SUCCESS',
      },
    }),
    prisma.userActivityLog.create({
      data: {
        userId: regularUser.id,
        actorType: ActorType.SYSTEM,
        companyId: companies[0].id,
        actionType: ActionType.EINVOICE_PAID,
        targetType: 'Einvoice',
        targetId: 'einvoice-1',
        details: {
          invoiceNumber: 'INV-001',
          amount: 1500.00,
          paymentMethod: 'Bank Transfer',
        },
        status: 'SUCCESS',
      },
    }),
  ])

  console.log('✅ Sample activity logs created')

  // Create sample notifications
  await Promise.all([
    prisma.notification.create({
      data: {
        userId: regularUser.id,
        type: NotificationType.EINVOICE_CREATED,
        title: 'Invoice Created Successfully',
        message: 'Your invoice INV-001 has been created and is ready for review.',
        priority: NotificationPriority.NORMAL,
        link: '/invoices/inv-001',
        metadata: {
          invoiceNumber: 'INV-001',
          amount: 1500.00,
        },
        activityLogId: activityLogs[0].id,
      },
    }),
    prisma.notification.create({
      data: {
        userId: regularUser.id,
        type: NotificationType.EINVOICE_PAYMENT_RECEIVED,
        title: 'Payment Received',
        message: 'Payment of $1,500.00 has been received for invoice INV-001.',
        priority: NotificationPriority.HIGH,
        link: '/invoices/inv-001',
        metadata: {
          invoiceNumber: 'INV-001',
          amount: 1500.00,
          paymentMethod: 'Bank Transfer',
        },
        activityLogId: activityLogs[1].id,
      },
    }),
    prisma.notification.create({
      data: {
        userId: regularUser.id,
        type: NotificationType.USER_ADDED_TO_COMPANY,
        title: 'Welcome to Acme Corporation',
        message: 'You have been added to Acme Corporation. You can now access company resources.',
        priority: NotificationPriority.HIGH,
        link: '/companies/company-1',
        metadata: {
          companyName: 'Acme Corporation',
          role: 'USER',
        },
      },
    }),
    prisma.notification.create({
      data: {
        userId: adminUser.id,
        type: NotificationType.SYSTEM_ANNOUNCEMENT,
        title: 'System Maintenance Scheduled',
        message: 'Scheduled maintenance will occur on Sunday at 2:00 AM UTC.',
        priority: NotificationPriority.NORMAL,
        metadata: {
          maintenanceDate: '2024-01-28T02:00:00Z',
          estimatedDuration: '2 hours',
        },
      },
    }),
  ])

  console.log('✅ Sample notifications created')

  console.log('🎉 Database seeding completed!')
  console.log('\n📋 Login credentials:')
  console.log('Admin: <EMAIL> / admin123')
  console.log('User: <EMAIL> / user123')
  console.log('\n🔔 Features available:')
  console.log('- Real-time notifications')
  console.log('- Activity logging')
  console.log('- Multi-tenancy support')
  console.log('- Socket.io integration')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
